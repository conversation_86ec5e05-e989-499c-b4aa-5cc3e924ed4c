import Link from 'next/link';
import Header from '../../../components/Header';
import Footer from '../../../components/Footer';
import ArticleCard from '../../../components/ArticleCard';
import { api } from '../../../lib/api';
import { notFound } from 'next/navigation';

async function getAuthorData(id) {
  try {
    const author = await api.authors.getById(id);
    return { author };
  } catch (error) {
    console.error('Error fetching author:', error);
    return null;
  }
}

export async function generateMetadata({ params }) {
  const data = await getAuthorData(params.id);
  
  if (!data?.author) {
    return {
      title: 'Author Not Found - <PERSON><PERSON><PERSON>',
      description: 'The requested author could not be found.',
    };
  }
  
  const { author } = data;
  return {
    title: `${author.name} - Authors - <PERSON><PERSON><PERSON>`,
    description: author.bio || `Read articles and insights from ${author.name}.`,
  };
}

export default async function AuthorPage({ params }) {
  const { id } = params;
  const data = await getAuthorData(id);
  
  if (!data?.author) {
    notFound();
  }
  
  const { author } = data;

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Author Header */}
      <section className="pt-12 pb-8 bg-primary text-white">
        <div className="container-custom">
          <Link href="/authors" className="inline-block mb-4 hover:underline">
            ← Back to Authors
          </Link>
          <div className="grid md:grid-cols-3 gap-8 items-center">
            <div className="md:col-span-2">
              <h1 className="mb-4">{author.name}</h1>
              {author.bio && (
                <p className="text-lg mb-6">
                  {author.bio}
                </p>
              )}
              
              {author.articles && author.articles.length > 0 && (
                <div className="text-lg">
                  <span className="font-medium">{author.articles.length}</span> 
                  <span className="ml-1">
                    article{author.articles.length !== 1 ? 's' : ''} published
                  </span>
                </div>
              )}
            </div>
            
            {/* Author Image */}
            <div className="flex justify-center">
              <div className="w-48 h-48 rounded-full bg-white bg-opacity-10 overflow-hidden">
                {author.image ? (
                  <img 
                    src={author.image} 
                    alt={author.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-white text-6xl font-semibold">
                    {author.name.charAt(0)}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Author Bio */}
      {author.bio && (
        <section className="py-12 bg-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-2xl font-semibold mb-6">About {author.name}</h2>
              <div className="prose prose-lg max-w-none">
                <p>{author.bio}</p>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Author's Articles */}
      {author.articles && author.articles.length > 0 && (
        <section className="py-12 bg-neutral-100">
          <div className="container-custom">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-2xl font-semibold mb-8">Articles by {author.name}</h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {author.articles.map((article) => (
                  <div key={article.slug} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                    <div className="p-6">
                      <Link href={`/articles/${article.slug}`}>
                        <h3 className="text-xl mb-2 hover:text-primary transition-colors">
                          {article.title}
                        </h3>
                      </Link>
                      
                      {article.published_date && (
                        <div className="text-sm text-neutral-500 mb-3">
                          {new Date(article.published_date).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}
                        </div>
                      )}
                      
                      <Link 
                        href={`/articles/${article.slug}`}
                        className="text-primary font-medium hover:text-primary-dark"
                      >
                        Read Article →
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
              
              {author.articles.length > 6 && (
                <div className="text-center mt-8">
                  <Link 
                    href={`/articles?author=${author.slug}`}
                    className="btn btn-outline"
                  >
                    View All Articles by {author.name}
                  </Link>
                </div>
              )}
            </div>
          </div>
        </section>
      )}

      {/* No Articles Message */}
      {(!author.articles || author.articles.length === 0) && (
        <section className="py-12 bg-neutral-100">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-2xl font-semibold mb-4">Articles Coming Soon</h2>
              <p className="text-lg text-neutral-600">
                {author.name} hasn't published any articles yet, but check back soon for inspiring content!
              </p>
            </div>
          </div>
        </section>
      )}

      {/* Call to Action */}
      <section className="py-12 bg-secondary-light">
        <div className="container-custom max-w-3xl mx-auto text-center">
          <h2 className="mb-4">Interested in Contributing?</h2>
          <p className="text-lg mb-8">
            If you have wisdom and insights to share with women seeking biblical guidance, we welcome guest contributors.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="btn btn-primary">
              Submit Your Writing Proposal
            </Link>
            <Link href="/authors" className="btn btn-outline">
              Meet All Our Authors
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

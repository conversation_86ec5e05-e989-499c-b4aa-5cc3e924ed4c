import Header from '../../components/Header';
import Footer from '../../components/Footer';
import { FaEnvelope, FaCheck } from 'react-icons/fa';

export const metadata = {
  title: 'Subscribe - <PERSON><PERSON><PERSON>',
  description: 'Subscribe to our newsletter for regular updates on biblical wisdom and inspiration for modern women.',
};

export default function SubscribePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Page Header */}
      <section className="bg-primary py-12 text-white">
        <div className="container-custom">
          <h1 className="mb-4">Subscribe to Our Newsletter</h1>
          <p className="text-lg max-w-3xl">
            Join our community and receive regular updates on biblical wisdom and inspiration for modern women.
          </p>
        </div>
      </section>

      {/* Subscription Form */}
      <section className="section bg-neutral-100">
        <div className="container-custom max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12">
            {/* Form */}
            <div>
              <h2 className="mb-6">Sign Up Today</h2>
              <form className="bg-white p-8 rounded-lg border border-neutral-200">
                <div className="mb-6">
                  <label htmlFor="firstName" className="block text-neutral-700 font-medium mb-2">First Name</label>
                  <input 
                    type="text" 
                    id="firstName" 
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter your first name"
                  />
                </div>
                <div className="mb-6">
                  <label htmlFor="lastName" className="block text-neutral-700 font-medium mb-2">Last Name</label>
                  <input 
                    type="text" 
                    id="lastName" 
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter your last name"
                  />
                </div>
                <div className="mb-6">
                  <label htmlFor="email" className="block text-neutral-700 font-medium mb-2">Email Address</label>
                  <input 
                    type="email" 
                    id="email" 
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter your email"
                  />
                </div>
                <div className="mb-6">
                  <label className="block text-neutral-700 font-medium mb-2">Interests (Optional)</label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input type="checkbox" id="biblical" className="mr-2" />
                      <label htmlFor="biblical">Biblical Women</label>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" id="modern" className="mr-2" />
                      <label htmlFor="modern">Modern Womanhood</label>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" id="lifestyle" className="mr-2" />
                      <label htmlFor="lifestyle">Lifestyle</label>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" id="faith" className="mr-2" />
                      <label htmlFor="faith">Faith & Family</label>
                    </div>
                  </div>
                </div>
                <div className="mb-6">
                  <div className="flex items-center">
                    <input type="checkbox" id="privacy" className="mr-2" />
                    <label htmlFor="privacy" className="text-sm">
                      I agree to the <a href="/privacy" className="text-primary hover:text-primary-dark">Privacy Policy</a> and consent to receiving email updates.
                    </label>
                  </div>
                </div>
                <button type="submit" className="btn btn-primary w-full flex items-center justify-center gap-2">
                  Subscribe Now <FaEnvelope />
                </button>
              </form>
            </div>

            {/* Benefits */}
            <div>
              <h2 className="mb-6">Newsletter Benefits</h2>
              <div className="bg-white p-8 rounded-lg border border-neutral-200 mb-8">
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <div className="bg-primary-light p-2 rounded-full text-primary mr-3 mt-1">
                      <FaCheck />
                    </div>
                    <div>
                      <h3 className="font-medium">Weekly Inspiration</h3>
                      <p className="text-neutral-600">
                        Receive uplifting content and biblical wisdom delivered directly to your inbox.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-primary-light p-2 rounded-full text-primary mr-3 mt-1">
                      <FaCheck />
                    </div>
                    <div>
                      <h3 className="font-medium">Exclusive Content</h3>
                      <p className="text-neutral-600">
                        Get access to subscriber-only articles, devotionals, and resources.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-primary-light p-2 rounded-full text-primary mr-3 mt-1">
                      <FaCheck />
                    </div>
                    <div>
                      <h3 className="font-medium">Early Announcements</h3>
                      <p className="text-neutral-600">
                        Be the first to know about upcoming events, partnerships, and new features.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-primary-light p-2 rounded-full text-primary mr-3 mt-1">
                      <FaCheck />
                    </div>
                    <div>
                      <h3 className="font-medium">Community Connection</h3>
                      <p className="text-neutral-600">
                        Join a supportive community of women seeking to grow in faith and purpose.
                      </p>
                    </div>
                  </li>
                </ul>
              </div>

              {/* Testimonial */}
              <div className="bg-secondary-light p-8 rounded-lg">
                <div className="text-accent text-4xl font-serif mb-3">"</div>
                <p className="text-lg italic mb-4">
                  The weekly newsletter has become a highlight in my inbox. The practical wisdom and encouragement have made a real difference in how I approach my daily challenges.
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-neutral-300 mr-3"></div>
                  <div>
                    <div className="font-medium">Amanda Wilson</div>
                    <div className="text-sm text-neutral-700">Subscriber since 2022</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

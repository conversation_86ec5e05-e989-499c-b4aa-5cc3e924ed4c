import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <div className="flex-grow flex items-center justify-center py-12">
        <div className="container-custom text-center">
          <div className="mb-8">
            <span className="text-8xl font-bold text-primary">404</span>
          </div>
          <h1 className="text-4xl md:text-5xl mb-4">Page Not Found</h1>
          <p className="text-xl text-neutral-600 mb-8 max-w-2xl mx-auto">
            We're sorry, but the page you're looking for doesn't exist or has been moved.
          </p>
          <div className="space-y-4 md:space-y-0 md:space-x-4">
            <Link href="/" className="btn btn-primary inline-block">
              Return to Home
            </Link>
            <Link href="/articles" className="btn btn-outline inline-block">
              Explore Articles
            </Link>
          </div>
          
          <div className="mt-16">
            <h2 className="text-2xl mb-6">You might be interested in:</h2>
            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <Link href="/articles" className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                <h3 className="text-xl mb-2 text-primary">Articles</h3>
                <p className="text-neutral-600">
                  Explore our collection of inspiring articles on biblical principles and modern womanhood.
                </p>
              </Link>
              <Link href="/testimonies" className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                <h3 className="text-xl mb-2 text-primary">Testimonies</h3>
                <p className="text-neutral-600">
                  Read stories from women who have found strength through biblical wisdom.
                </p>
              </Link>
              <Link href="/about" className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                <h3 className="text-xl mb-2 text-primary">About Us</h3>
                <p className="text-neutral-600">
                  Learn about our mission to inspire women through biblical principles.
                </p>
              </Link>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

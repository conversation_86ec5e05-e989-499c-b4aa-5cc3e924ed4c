import Link from 'next/link';
import Image from 'next/image';

export default function AuthorCard({ author }) {
  const {
    id,
    name,
    slug,
    bio,
    image,
    articles = [],
  } = author;

  return (
    <div className="bg-white rounded-lg overflow-hidden border border-neutral-200 hover:border-neutral-300 transition-colors">
      <div className="p-6">
        {/* Author image and basic info */}
        <div className="flex items-start gap-4 mb-4">
          <div className="w-16 h-16 rounded-full bg-neutral-200 overflow-hidden flex-shrink-0">
            {image ? (
              <img 
                src={image} 
                alt={name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-neutral-500 text-xl font-semibold">
                {name.charAt(0)}
              </div>
            )}
          </div>
          
          <div className="flex-1">
            <Link href={`/authors/${id}`}>
              <h3 className="text-xl font-semibold mb-1 hover:text-primary transition-colors">
                {name}
              </h3>
            </Link>
            
            {articles.length > 0 && (
              <p className="text-sm text-neutral-500">
                {articles.length} article{articles.length !== 1 ? 's' : ''}
              </p>
            )}
          </div>
        </div>

        {/* Bio */}
        {bio && (
          <p className="text-neutral-600 mb-4 line-clamp-3">
            {bio}
          </p>
        )}

        {/* Recent articles */}
        {articles.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-neutral-700 mb-2">Recent Articles:</h4>
            <div className="space-y-1">
              {articles.slice(0, 3).map((article) => (
                <Link
                  key={article.slug}
                  href={`/articles/${article.slug}`}
                  className="block text-sm text-primary hover:text-primary-dark truncate"
                >
                  {article.title}
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* View profile link */}
        <div className="mt-4 pt-4 border-t border-neutral-200">
          <Link 
            href={`/authors/${id}`}
            className="text-primary font-medium hover:text-primary-dark"
          >
            View Profile
          </Link>
        </div>
      </div>
    </div>
  );
}

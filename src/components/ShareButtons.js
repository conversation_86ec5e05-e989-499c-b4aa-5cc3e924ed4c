'use client';

import { useState } from 'react';
import { FaShare, FaFacebook, FaTwitter, FaLinkedin, FaWhatsapp, FaTelegram, FaEnvelope, FaCopy, FaCheck, FaInstagram } from 'react-icons/fa';
import { socialShareUrls, openShareWindow, copyToClipboard, generateShareData } from '../lib/socialShare';
import { SITE_CONFIG } from '../lib/constants';

export default function ShareButtons({ 
  item, 
  type = 'article', 
  title = 'Share this Article',
  className = '',
  showTitle = true,
  compact = false 
}) {
  const [copied, setCopied] = useState(false);
  const [instagramCopied, setInstagramCopied] = useState(false);

  // Generate sharing data
  const shareData = generateShareData(item, type, SITE_CONFIG.url);

  const handleShare = (platform) => {
    let shareUrl = '';

    switch (platform) {
      case 'facebook':
        shareUrl = socialShareUrls.facebook(shareData.url, shareData.title);
        break;
      case 'twitter':
        shareUrl = socialShareUrls.twitter(shareData.url, shareData.title, shareData.hashtags);
        break;
      case 'linkedin':
        shareUrl = socialShareUrls.linkedin(shareData.url, shareData.title, shareData.description);
        break;
      case 'whatsapp':
        shareUrl = socialShareUrls.whatsapp(shareData.url, shareData.title);
        break;
      case 'telegram':
        shareUrl = socialShareUrls.telegram(shareData.url, shareData.title);
        break;
      case 'email':
        shareUrl = socialShareUrls.email(shareData.url, shareData.title, shareData.description);
        break;
      case 'instagram':
        const instagramData = socialShareUrls.instagram(shareData.url, shareData.title);
        handleInstagramShare(instagramData.content);
        return;
      default:
        return;
    }

    if (platform === 'email') {
      window.location.href = shareUrl;
    } else {
      openShareWindow(shareUrl);
    }
  };

  const handleCopyLink = async () => {
    const success = await copyToClipboard(shareData.url);
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleInstagramShare = async (content) => {
    const success = await copyToClipboard(content);
    if (success) {
      setInstagramCopied(true);
      setTimeout(() => setInstagramCopied(false), 3000);
    }
  };

  // Use native Web Share API if available (mobile devices)
  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: shareData.title,
          text: shareData.description,
          url: shareData.url,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    }
  };

  const shareButtons = [
    {
      name: 'Facebook',
      icon: FaFacebook,
      color: 'bg-primary text-white hover:bg-primary-dark active:bg-primary-dark',
      onClick: () => handleShare('facebook'),
    },
    {
      name: 'Twitter',
      icon: FaTwitter,
      color: 'bg-accent text-white hover:bg-accent-dark active:bg-accent-dark',
      onClick: () => handleShare('twitter'),
    },
    {
      name: 'LinkedIn',
      icon: FaLinkedin,
      color: 'bg-primary-light text-primary-dark hover:bg-primary active:bg-primary',
      onClick: () => handleShare('linkedin'),
    },
    {
      name: 'WhatsApp',
      icon: FaWhatsapp,
      color: 'bg-secondary text-primary hover:bg-secondary-dark hover:text-primary-dark active:bg-secondary-dark',
      onClick: () => handleShare('whatsapp'),
    },
    {
      name: 'Telegram',
      icon: FaTelegram,
      color: 'bg-accent-light text-accent-dark hover:bg-accent active:bg-accent hover:text-white',
      onClick: () => handleShare('telegram'),
    },
    {
      name: 'Email',
      icon: FaEnvelope,
      color: 'bg-neutral-600 text-white hover:bg-primary-dark active:bg-primary-dark',
      onClick: () => handleShare('email'),
    },
    {
      name: 'Instagram',
      icon: FaInstagram,
      color: 'bg-accent text-white hover:bg-accent-dark active:bg-accent-dark',
      onClick: () => handleShare('instagram'),
    },
  ];

  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {/* Native share button for mobile */}
        {typeof navigator !== 'undefined' && navigator.share && (
          <button
            onClick={handleNativeShare}
            className="p-2 bg-primary text-white rounded-full hover:bg-primary-dark active:bg-primary-dark transition-colors"
            title="Share"
          >
            <FaShare className="w-4 h-4" />
          </button>
        )}
        
        {/* Main share buttons */}
        {shareButtons.slice(0, 3).map((button) => (
          <button
            key={button.name}
            onClick={button.onClick}
            className={`p-2 text-white rounded-full transition-colors ${button.color}`}
            title={`Share on ${button.name}`}
          >
            <button.icon className="w-4 h-4" />
          </button>
        ))}
        
        {/* Copy link button */}
        <button
          onClick={handleCopyLink}
          className={`p-2 text-white rounded-full transition-colors ${
            copied ? 'bg-success' : 'bg-secondary text-primary hover:bg-secondary-dark hover:text-primary-dark active:bg-secondary-dark'
          }`}
          title={copied ? 'Copied!' : 'Copy link'}
        >
          {copied ? <FaCheck className="w-4 h-4" /> : <FaCopy className="w-4 h-4" />}
        </button>
      </div>
    );
  }

  return (
    <div className={className}>
      {showTitle && (
        <h3 className="text-xl mb-4 flex items-center">
          <FaShare className="mr-2" /> {title}
        </h3>
      )}
      
      <div className="flex flex-wrap gap-3">
        {/* Native share button for mobile */}
        {typeof navigator !== 'undefined' && navigator.share && (
          <button
            onClick={handleNativeShare}
            className="p-3 bg-primary text-white rounded-full hover:bg-primary-dark active:bg-primary-dark transition-colors"
            title="Share"
          >
            <FaShare />
          </button>
        )}
        
        {/* Social media buttons */}
        {shareButtons.map((button) => (
          <button
            key={button.name}
            onClick={button.onClick}
            className={`p-3 text-white rounded-full transition-colors ${button.color}`}
            title={`Share on ${button.name}`}
          >
            <button.icon />
          </button>
        ))}
        
        {/* Copy link button */}
        <button
          onClick={handleCopyLink}
          className={`p-3 rounded-full transition-colors ${
            copied ? 'bg-success text-white' : 'bg-secondary text-primary hover:bg-secondary-dark hover:text-primary-dark active:bg-secondary-dark'
          }`}
          title={copied ? 'Copied!' : 'Copy link'}
        >
          {copied ? <FaCheck /> : <FaCopy />}
        </button>
      </div>
      
      {copied && (
        <p className="text-sm text-success mt-2">Link copied to clipboard!</p>
      )}

      {instagramCopied && (
        <p className="text-sm text-success mt-2">Instagram content copied! Open Instagram and paste to share.</p>
      )}
    </div>
  );
}

# Umugore <PERSON>wa Website

A Next.js website for inspiring women through biblical principles. This platform provides articles, testimonies, and community support for modern women seeking guidance through faith.

## Recent Updatesss

### Social Sharing & UI Improvements
- ✅ **Fixed Share Buttons**: Implemented functional social media sharing for articles and testimonies
- ✅ **Enhanced Open Graph**: Added proper meta tags for better link previews on social platforms
- ✅ **Removed Box Shadows**: Updated all cards to use modern border styling instead of shadows
- ✅ **Mobile-Friendly Sharing**: Added native Web Share API support for mobile devices
- ✅ **Instagram Sharing**: Added Instagram sharing with copy-to-clipboard functionality
- ✅ **Search Functionality**: Fixed header search with modal interface and URL parameter support
- ✅ **Hero Button Navigation**: Made hero section buttons functional with proper navigation
- ✅ **Centralized Design System**: Unified all styling variables in a single source of truth
- ✅ **Production Build Fix**: Resolved Next.js Suspense boundary issue for static generation

### Featuresss
- **Social Sharing**: Share articles and testimonies on Facebook, Twitter, LinkedIn, WhatsApp, Telegram, Instagram, and Email
- **Copy Link**: One-click link copying with visual feedback
- **Instagram Sharing**: Copy formatted content for manual Instagram sharing
- **Search Modal**: Beautiful search interface with popular topic suggestions
- **URL Search Parameters**: Direct linking to search results
- **Hero Navigation**: Functional buttons that navigate to articles and newsletter signup
- **Rich Link Previews**: Proper Open Graph and Twitter Card meta tags for social media
- **Modern UI**: Clean, professional design without box shadows
- **Design System**: Centralized CSS variables for colors, typography, spacing, and more
- **Theme Customization**: Easy color scheme changes by updating CSS variables
- **Production Ready**: Optimized build with proper static generation and Suspense boundaries

## Getting Started

First, install dependencies:

```bash
npm install
```

Then run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Production Build

To create a production build:

```bash
npm run build
```

To start the production server:

```bash
npm start
```

The application is optimized for static generation and includes proper Suspense boundaries for client-side features.

## Setup Notes

### Open Graph Image
To complete the social sharing setup, add a default Open Graph image:
- Create an image file named `og-image.jpg` in the `public/` directory
- Recommended size: 1200x630 pixels
- This image will be used when sharing links on social media platforms

### Design System
The application uses a centralized design system with CSS variables:
- **Colors**: All colors are defined in `src/app/globals.css` as CSS variables
- **Typography**: Font families, sizes, and weights are centralized
- **Spacing**: Consistent spacing scale for margins, padding, and gaps
- **Components**: Reusable component classes for buttons, cards, forms

To change the color scheme, update the CSS variables in `src/app/globals.css`:
```css
:root {
  --color-primary: #YOUR_NEW_COLOR;
  --color-primary-light: #YOUR_LIGHT_VARIANT;
  --color-primary-dark: #YOUR_DARK_VARIANT;
}
```

See `DESIGN_SYSTEM.md` for complete documentation.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
